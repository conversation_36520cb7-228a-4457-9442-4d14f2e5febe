document.addEventListener('DOMContentLoaded', () => {
  // Back to Top Button functionality
  const backToTopButton = document.getElementById('back-to-top');

  // Show/hide back to top button based on scroll position
  window.addEventListener('scroll', function() {
    if (window.pageYOffset > 300) {
      backToTopButton.classList.add('visible');
    } else {
      backToTopButton.classList.remove('visible');
    }
  });

  // Scroll to top when button is clicked
  backToTopButton.addEventListener('click', function() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  });

  // Mobile Menu Animation
  const menuBtn = document.querySelector('.header-menuImg');
  const navList = document.querySelector('.header-nav');
  const body = document.querySelector('body');

  if (menuBtn) {
    menuBtn.addEventListener('click', function() {
      body.classList.toggle('menu-open');
      navList.classList.toggle('header-nav__active');
      body.classList.toggle('stop-scroll');
    });
  }

  // Close menu when clicking on links
  if (navList) {
    navList.addEventListener('click', (event) => {
      if (event.target.classList.contains('header-nav__link')) {
        navList.classList.remove('header-nav__active');
        body.classList.remove('stop-scroll');
        body.classList.remove('menu-open');
      }
    });
  }

  // Close menu when clicking outside
  document.addEventListener('click', function(event) {
    if (navList && navList.classList.contains('header-nav__active') &&
        !navList.contains(event.target) &&
        !menuBtn.contains(event.target)) {
      navList.classList.remove('header-nav__active');
      body.classList.remove('stop-scroll');
      body.classList.remove('menu-open');
    }
  });

  // Form functionality starts here
  const form = document.getElementById('quoteForm');
  const submitBtn = document.getElementById('submitForm');

  const acceptDisclaimer = document.getElementById('acceptDisclaimer');
  const resultMessage = document.getElementById('result-message');

  // Form fields
  const fields = {
    empresa: document.getElementById('empresa'),
    nombre: document.getElementById('nombre'),
    correo: document.getElementById('correo'),
    direccion_origen: document.getElementById('direccion_origen'),
    cp_origen: document.getElementById('cp_origen'),
    direccion_destino: document.getElementById('direccion_destino'),
    cp_destino: document.getElementById('cp_destino'),
    tipo_mercancia: document.getElementById('tipo_mercancia'),
    numero_un: document.getElementById('numero_un'),
    piezas: document.getElementById('piezas'),
    peso: document.getElementById('peso'),
    dimensiones: document.getElementById('dimensiones'),
    descripcion_mercancia: document.getElementById('descripcion_mercancia')
  };

  // Unit selector for dimensions
  const dimensionesUnidad = document.getElementById('dimensiones_unidad');

  // UN Number field elements
  const numeroUnField = document.getElementById('numero_un_field');

  // Dimensions unit configuration
  const dimensionUnits = {
    cm: {
      label: 'cm',
      placeholder: 'Largo x Ancho x Alto (cm)',
      min: 1,
      max: 2000,
      step: 1
    },
    mm: {
      label: 'mm',
      placeholder: 'Largo x Ancho x Alto (mm)',
      min: 10,
      max: 20000,
      step: 1
    },
    in: {
      label: 'pulgadas',
      placeholder: 'Largo x Ancho x Alto (pulgadas)',
      min: 0.4,
      max: 78.7,
      step: 0.1
    }
  };

  // Track validation state for each field
  const validationState = {};
  Object.keys(fields).forEach(key => {
    validationState[key] = false;
    // Initialize hasBeenInteracted property
    fields[key].hasBeenInteracted = false;
  });

  // Add comprehensive event listeners for real-time validation
  Object.entries(fields).forEach(([key, field]) => {
    // Real-time validation on input (only if field has been interacted with)
    field.addEventListener('input', () => {
      // Mark field as interacted with
      field.hasBeenInteracted = true;

      // Only validate if field has been interacted with
      if (field.hasBeenInteracted) {
        // Debounce validation for better performance
        clearTimeout(field.validationTimeout);
        field.validationTimeout = setTimeout(() => {
          validateField(field, key);
          updateFormState();
        }, 300);
      }
    });

    // Immediate validation on blur (only if field has been interacted with)
    field.addEventListener('blur', () => {
      // Mark field as interacted with
      field.hasBeenInteracted = true;

      clearTimeout(field.validationTimeout);
      validateField(field, key);
      updateFormState();
    });

    // Special handling for select elements
    if (field.tagName === 'SELECT') {
      field.addEventListener('change', () => {
        validateField(field, key);
        updateFormState();
      });
    }

    // Add input formatting helpers
    if (key === 'cp_origen' || key === 'cp_destino') {
      field.addEventListener('input', (e) => {
        // Only allow numbers and limit to 5 digits
        e.target.value = e.target.value.replace(/\D/g, '').substring(0, 5);
      });
    }

    if (key === 'piezas') {
      field.addEventListener('input', (e) => {
        // Only allow positive integers
        e.target.value = e.target.value.replace(/\D/g, '');
      });
    }

    if (key === 'peso') {
      field.addEventListener('input', (e) => {
        // Allow numbers with up to 2 decimal places
        let value = e.target.value.replace(/[^\d.]/g, '');
        const parts = value.split('.');
        if (parts.length > 2) {
          value = parts[0] + '.' + parts.slice(1).join('');
        }
        if (parts[1] && parts[1].length > 2) {
          value = parts[0] + '.' + parts[1].substring(0, 2);
        }
        e.target.value = value;
      });
    }

    if (key === 'dimensiones') {
      field.addEventListener('input', (e) => {
        // Auto-format dimensions as user types
        let value = e.target.value.replace(/[^\d\sx×X]/g, '');
        // Replace multiple spaces with single space
        value = value.replace(/\s+/g, ' ');
        // Auto-add 'x' between numbers
        value = value.replace(/(\d)\s+(\d)/g, '$1 x $2');
        e.target.value = value;
      });
    }

    if (key === 'numero_un') {
      field.addEventListener('input', (e) => {
        // Auto-format UN number as user types
        let value = e.target.value.toUpperCase();

        // Remove any characters that aren't UN or digits
        value = value.replace(/[^UN\d]/g, '');

        // If user starts typing numbers without UN, add UN prefix
        if (/^\d/.test(value)) {
          value = 'UN' + value;
        }

        // Ensure it starts with UN
        if (!value.startsWith('UN') && value.length > 0) {
          value = 'UN' + value.replace(/^UN/g, '');
        }

        // Limit to UN + 4 digits maximum
        if (value.length > 6) {
          value = value.substring(0, 6);
        }

        // Only allow digits after UN
        if (value.length > 2) {
          const unPart = value.substring(0, 2);
          const numberPart = value.substring(2).replace(/\D/g, '');
          value = unPart + numberPart;
        }

        e.target.value = value;
      });
    }
  });

  // Enable/disable submit button based on disclaimer acceptance and form validation
  acceptDisclaimer.addEventListener('change', () => {
    updateSubmitButtonState();
  });

  // Handle dimension unit changes
  dimensionesUnidad.addEventListener('change', (e) => {
    updateDimensionPlaceholder(e.target.value);
    // Re-validate dimensions field if it has been interacted with
    if (fields.dimensiones.hasBeenInteracted) {
      validateField(fields.dimensiones, 'dimensiones', true);
      updateFormState();
    }
  });

  // Handle tipo_mercancia changes for UN number field
  fields.tipo_mercancia.addEventListener('change', (e) => {
    handleTipoMercanciaChange(e.target.value);
  });

  // Handle form submission
  form.addEventListener('submit', async (e) => {
    e.preventDefault();

    if (!validateAllFields(true) || !acceptDisclaimer.checked) {
      showMessage('Por favor, complete todos los campos correctamente y acepte el aviso.', 'error');
      return;
    }

    // Disable submit button and show loading state
    submitBtn.disabled = true;
    submitBtn.textContent = 'Enviando...';

    try {
      const formData = new FormData();
      Object.entries(fields).forEach(([key, field]) => {
        formData.append(key, field.value.trim());
      });

      const response = await fetch('process_quote.php', {
        method: 'POST',
        body: formData
      });

      // Check if response is ok and has content
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get response text first to check if it's valid JSON
      const responseText = await response.text();

      if (!responseText.trim()) {
        throw new Error('Empty response from server');
      }

      let result;
      try {
        result = JSON.parse(responseText);
      } catch (jsonError) {
        console.error('JSON parsing error:', jsonError);
        console.error('Response text:', responseText);
        throw new Error('Invalid response format from server');
      }

      if (result.success) {
        showMessage('Su solicitud de cotización ha sido enviada exitosamente. Nos pondremos en contacto con usted pronto.', 'success');

        // Reset form and states
        form.reset();
        resetFormState();
        acceptDisclaimer.checked = false;
      } else {
        showMessage(result.message || 'Hubo un error al enviar su solicitud. Por favor, intente nuevamente.', 'error');
      }
    } catch (error) {
      console.error('Error:', error);

      // Provide more specific error messages based on error type
      let errorMessage = 'Hubo un error al enviar su solicitud. Por favor, intente nuevamente.';

      if (error.message.includes('HTTP error')) {
        errorMessage = 'Error del servidor. Por favor, intente nuevamente más tarde.';
      } else if (error.message.includes('Invalid response format')) {
        errorMessage = 'Error de comunicación con el servidor. Por favor, intente nuevamente.';
      } else if (error.message.includes('Empty response')) {
        errorMessage = 'No se recibió respuesta del servidor. Por favor, intente nuevamente.';
      }

      showMessage(errorMessage, 'error');
    } finally {
      submitBtn.disabled = false;
      submitBtn.textContent = 'Enviar Solicitud';
    }
  });

  // Enhanced validation functions
  function validateField(field, fieldKey, forceValidation = false) {
    const value = field.value.trim();
    const errorSpan = document.getElementById(`error-${field.id}`);
    let errorMessage = '';
    let isValid = true;

    // Clear previous states
    field.classList.remove('error-input', 'valid-input');
    errorSpan.style.display = 'none';
    errorSpan.textContent = '';

    // Skip validation if field hasn't been interacted with and not forced
    if (!field.hasBeenInteracted && !forceValidation) {
      validationState[fieldKey] = false; // Don't assume valid until validated
      return false;
    }

    // Skip validation if field is empty and not required for real-time validation
    if (!value && !field.hasAttribute('required')) {
      validationState[fieldKey] = true;
      return true;
    }

    // Comprehensive validation rules
    switch (field.id) {
      case 'empresa':
        if (!value) {
          errorMessage = 'El nombre de la empresa es obligatorio.';
          isValid = false;
        } else if (value.length < 2) {
          errorMessage = 'El nombre debe tener al menos 2 caracteres.';
          isValid = false;
        } else if (value.length > 100) {
          errorMessage = 'El nombre no puede exceder 100 caracteres.';
          isValid = false;
        } else if (!/^[\w\sáéíóúÁÉÍÓÚñÑ\.\-&(),]{2,}$/u.test(value)) {
          errorMessage = 'Solo se permiten letras, números y símbolos básicos (.,&-()).';
          isValid = false;
        }
        break;

      case 'nombre':
        if (!value) {
          errorMessage = 'El nombre del contacto es obligatorio.';
          isValid = false;
        } else if (value.length < 2) {
          errorMessage = 'El nombre debe tener al menos 2 caracteres.';
          isValid = false;
        } else if (value.length > 80) {
          errorMessage = 'El nombre no puede exceder 80 caracteres.';
          isValid = false;
        } else if (!/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s\-\.]{2,}$/u.test(value)) {
          errorMessage = 'Solo se permiten letras, espacios, guiones y puntos.';
          isValid = false;
        }
        break;

      case 'correo':
        if (!value) {
          errorMessage = 'El correo electrónico es obligatorio.';
          isValid = false;
        } else if (value.length > 254) {
          errorMessage = 'El correo electrónico es demasiado largo.';
          isValid = false;
        } else if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/i.test(value)) {
          errorMessage = 'Ingrese un correo electrónico válido.';
          isValid = false;
        } else {
          // Additional email validation
          const emailParts = value.split('@');
          if (emailParts[0].length > 64) {
            errorMessage = 'La parte local del correo es demasiado larga.';
            isValid = false;
          } else if (emailParts[1].length > 253) {
            errorMessage = 'El dominio del correo es demasiado largo.';
            isValid = false;
          }
        }
        break;

      case 'direccion_origen':
      case 'direccion_destino':
        const direccionLabel = field.id === 'direccion_origen' ? 'origen' : 'destino';
        if (!value) {
          errorMessage = `La dirección de ${direccionLabel} es obligatoria.`;
          isValid = false;
        } else if (value.length < 15) {
          errorMessage = `La dirección debe ser más específica (mínimo 15 caracteres).`;
          isValid = false;
        } else if (value.length > 200) {
          errorMessage = 'La dirección no puede exceder 200 caracteres.';
          isValid = false;
        } else if (!/^[a-zA-ZáéíóúÁÉÍÓÚñÑ0-9\s\-.#,°]+$/u.test(value)) {
          errorMessage = 'La dirección contiene caracteres no válidos.';
          isValid = false;
        }
        break;

      case 'cp_origen':
      case 'cp_destino':
        const cpLabel = field.id === 'cp_origen' ? 'origen' : 'destino';
        if (!value) {
          errorMessage = `El código postal de ${cpLabel} es obligatorio.`;
          isValid = false;
        } else if (!/^\d{5}$/.test(value)) {
          errorMessage = 'El código postal debe tener exactamente 5 dígitos.';
          isValid = false;
        } else if (parseInt(value) < 1000 || parseInt(value) > 99999) {
          errorMessage = 'Ingrese un código postal válido de México.';
          isValid = false;
        }
        break;

      case 'tipo_mercancia':
        const validTypes = ['sobredimensionado', 'hazmat', 'perecedero', 'carga_general'];
        if (!value) {
          errorMessage = 'Debe seleccionar un tipo de mercancía.';
          isValid = false;
        } else if (!validTypes.includes(value)) {
          errorMessage = 'Seleccione una opción válida.';
          isValid = false;
        }
        break;

      case 'piezas':
        if (!value) {
          errorMessage = 'El número de piezas es obligatorio.';
          isValid = false;
        } else if (!/^\d+$/.test(value)) {
          errorMessage = 'Solo se permiten números enteros.';
          isValid = false;
        } else if (parseInt(value) < 1) {
          errorMessage = 'Debe ser al menos 1 pieza.';
          isValid = false;
        } else if (parseInt(value) > 10000) {
          errorMessage = 'El número de piezas no puede exceder 10,000.';
          isValid = false;
        }
        break;

      case 'peso':
        if (!value) {
          errorMessage = 'El peso es obligatorio.';
          isValid = false;
        } else if (!/^\d+(\.\d{1,2})?$/.test(value)) {
          errorMessage = 'Ingrese un peso válido (ej: 25.5).';
          isValid = false;
        } else if (parseFloat(value) <= 0) {
          errorMessage = 'El peso debe ser mayor a 0.';
          isValid = false;
        } else if (parseFloat(value) > 50000) {
          errorMessage = 'El peso no puede exceder 50,000 kg.';
          isValid = false;
        }
        break;

      case 'dimensiones':
        if (!value) {
          errorMessage = 'Las dimensiones son obligatorias.';
          isValid = false;
        } else {
          // Get current unit configuration
          const currentUnit = dimensionesUnidad.value;
          const unitConfig = dimensionUnits[currentUnit];

          // Check format based on unit (allow decimals for inches)
          const formatRegex = currentUnit === 'in'
            ? /^[\d.]+\s*[xX×]\s*[\d.]+\s*[xX×]\s*[\d.]+$/
            : /^\d+\s*[xX×]\s*\d+\s*[xX×]\s*\d+$/;

          if (!formatRegex.test(value)) {
            errorMessage = `Formato: Largo x Ancho x Alto (ej: ${currentUnit === 'in' ? '39.4 x 19.7 x 11.8' : '100 x 50 x 30'}).`;
            isValid = false;
          } else {
            // Extract dimensions and validate ranges
            const matches = value.match(/([\d.]+)\s*[xX×]\s*([\d.]+)\s*[xX×]\s*([\d.]+)/);
            if (matches) {
              const [, largo, ancho, alto] = matches.map(Number);

              if (largo > unitConfig.max || ancho > unitConfig.max || alto > unitConfig.max) {
                errorMessage = `Las dimensiones no pueden exceder ${unitConfig.max} ${unitConfig.label} en ninguna medida.`;
                isValid = false;
              } else if (largo < unitConfig.min || ancho < unitConfig.min || alto < unitConfig.min) {
                errorMessage = `Todas las dimensiones deben ser mayores a ${unitConfig.min} ${unitConfig.label}.`;
                isValid = false;
              }
            }
          }
        }
        break;

      case 'numero_un':
        // Only validate if Hazmat is selected (field is required)
        const isHazmatSelected = fields.tipo_mercancia.value === 'hazmat';

        if (isHazmatSelected) {
          if (!value) {
            errorMessage = 'El número UN es obligatorio para materiales peligrosos.';
            isValid = false;
          } else if (!/^UN\d{4}$/i.test(value)) {
            errorMessage = 'Formato requerido: UN seguido de 4 dígitos (ej: UN1203).';
            isValid = false;
          }
        } else {
          // If Hazmat is not selected, field is always valid
          isValid = true;
        }
        break;

      case 'descripcion_mercancia':
        if (!value) {
          errorMessage = 'La descripción de la mercancía es obligatoria.';
          isValid = false;
        } else if (value.length < 10) {
          errorMessage = 'La descripción debe tener al menos 10 caracteres.';
          isValid = false;
        } else if (value.length > 500) {
          errorMessage = 'La descripción no puede exceder 500 caracteres.';
          isValid = false;
        } else if (!/^[a-zA-ZáéíóúÁÉÍÓÚñÑ0-9\s\-.,;:()\/&+%$#@!]+$/u.test(value)) {
          errorMessage = 'La descripción contiene caracteres no válidos.';
          isValid = false;
        }
        break;
    }

    // Update validation state
    validationState[fieldKey] = isValid;

    // Apply visual feedback
    if (!isValid) {
      field.classList.add('error-input');
      errorSpan.textContent = errorMessage;
      errorSpan.style.display = 'block';
    } else {
      field.classList.add('valid-input');
    }

    return isValid;
  }

  // Validate all fields with optional force parameter
  function validateAllFields(forceValidation = false) {
    let isValid = true;

    Object.entries(fields).forEach(([key, field]) => {
      const fieldValid = validateField(field, key, forceValidation);
      if (!fieldValid) {
        isValid = false;
      }
    });

    return isValid;
  }

  // Update form state and button availability (only for specific field validation)
  function updateFormState() {
    updateSubmitButtonState();
  }

  // Update submit button state based on form validation and disclaimer acceptance
  function updateSubmitButtonState() {
    // For submit button, require ALL fields to be valid (force validation)
    const allFieldsValid = validateAllFields(true);
    const disclaimerAccepted = acceptDisclaimer.checked;

    // Update button state - only enable if ALL fields are valid AND disclaimer is accepted
    if (allFieldsValid && disclaimerAccepted) {
      submitBtn.disabled = false;
      submitBtn.classList.remove('btn-disabled');
    } else {
      submitBtn.disabled = true;
      submitBtn.classList.add('btn-disabled');
    }
  }



  // Update dimension placeholder based on selected unit
  function updateDimensionPlaceholder(unit) {
    const unitConfig = dimensionUnits[unit];
    if (unitConfig && fields.dimensiones) {
      fields.dimensiones.placeholder = unitConfig.placeholder;
    }
  }

  // Handle tipo_mercancia change for UN number field visibility
  function handleTipoMercanciaChange(tipoValue) {
    const isHazmat = tipoValue === 'hazmat';

    if (isHazmat) {
      // Show UN number field
      numeroUnField.style.display = 'block';
      fields.numero_un.setAttribute('required', 'required');

      // Add to validation if not already there
      if (!validationState.hasOwnProperty('numero_un')) {
        validationState.numero_un = false;
      }
    } else {
      // Hide UN number field
      numeroUnField.style.display = 'none';
      fields.numero_un.removeAttribute('required');
      fields.numero_un.value = ''; // Clear value

      // Remove from validation
      validationState.numero_un = true; // Set as valid when not required

      // Clear any error messages
      const errorSpan = document.getElementById('error-numero_un');
      if (errorSpan) {
        errorSpan.style.display = 'none';
        errorSpan.textContent = '';
      }

      // Remove visual validation classes
      fields.numero_un.classList.remove('error-input', 'valid-input');
      fields.numero_un.hasBeenInteracted = false;
    }

    // Update form state
    updateFormState();
  }

  // Enhanced form state management
  function resetFormState() {
    Object.keys(validationState).forEach(key => {
      validationState[key] = false;
    });

    Object.values(fields).forEach(field => {
      field.classList.remove('error-input', 'valid-input');
      field.hasBeenInteracted = false; // Reset interaction state

      // Clear validation timeouts
      if (field.validationTimeout) {
        clearTimeout(field.validationTimeout);
        field.validationTimeout = null;
      }

      const errorSpan = document.getElementById(`error-${field.id}`);
      if (errorSpan) {
        errorSpan.style.display = 'none';
        errorSpan.textContent = '';
      }
    });

    // Reset submit button state
    submitBtn.disabled = true;
    submitBtn.classList.add('btn-disabled');
    submitBtn.textContent = 'Confirmar y Enviar';

    // Reset dimension unit selector to default (cm)
    dimensionesUnidad.value = 'cm';
    updateDimensionPlaceholder('cm');

    // Reset UN number field visibility and value
    numeroUnField.style.display = 'none';
    fields.numero_un.removeAttribute('required');
    fields.numero_un.value = '';
    validationState.numero_un = true;

    updateFormState();
  }

  function showMessage(message, type) {
    if (type === 'success') {
      showSuccessMessage(message);
    } else {
      // Handle error messages as before
      resultMessage.textContent = message;
      resultMessage.className = `result-message ${type}`;
      resultMessage.style.display = 'block';

      // Scroll to message
      resultMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }

  // Enhanced success message with auto-restart functionality
  function showSuccessMessage(message) {
    // Create enhanced success message HTML
    resultMessage.innerHTML = `
      <div class="success-content">
        <div class="success-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22,4 12,14.01 9,11.01"></polyline>
          </svg>
        </div>
        <h3 class="success-title">¡Solicitud Enviada Exitosamente!</h3>
        <p class="success-message">${message}</p>
        <div class="success-actions">
          <button type="button" id="newQuoteBtn" class="btn-new-quote">
            Nueva Cotización
          </button>
          <div class="auto-restart-info">
            <p>El formulario se reiniciará automáticamente en <span id="countdown">15</span> segundos</p>
          </div>
        </div>
      </div>
    `;

    resultMessage.className = 'result-message success enhanced';
    resultMessage.style.display = 'block';

    // Scroll to message
    resultMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Start countdown and auto-restart
    startAutoRestart();
  }

  // Auto-restart functionality with countdown
  let autoRestartTimer = null;
  let countdownInterval = null;

  function startAutoRestart() {
    let timeLeft = 15;
    const countdownElement = document.getElementById('countdown');
    const newQuoteBtn = document.getElementById('newQuoteBtn');

    // Update countdown display
    function updateCountdown() {
      if (countdownElement) {
        countdownElement.textContent = timeLeft;
      }

      if (timeLeft <= 0) {
        clearInterval(countdownInterval);
        performFormRestart();
      } else {
        timeLeft--;
      }
    }

    // Start countdown interval (update every second)
    countdownInterval = setInterval(updateCountdown, 1000);

    // Set auto-restart timer
    autoRestartTimer = setTimeout(() => {
      performFormRestart();
    }, 15000);

    // Add event listener to "Nueva Cotización" button
    if (newQuoteBtn) {
      newQuoteBtn.addEventListener('click', () => {
        cancelAutoRestart();
        performFormRestart();
      });
    }
  }

  // Cancel auto-restart timers
  function cancelAutoRestart() {
    if (autoRestartTimer) {
      clearTimeout(autoRestartTimer);
      autoRestartTimer = null;
    }

    if (countdownInterval) {
      clearInterval(countdownInterval);
      countdownInterval = null;
    }
  }

  // Perform complete form restart
  function performFormRestart() {
    // Cancel any running timers
    cancelAutoRestart();

    // Hide success message
    resultMessage.style.display = 'none';

    // Reset form completely
    form.reset();
    resetFormState();
    acceptDisclaimer.checked = false;

    // Reset wizard to step 1
    showStep(1);

    // Clear any validation messages
    const validationMessage = document.getElementById('validation-message');
    if (validationMessage) {
      validationMessage.style.display = 'none';
    }

    // Scroll to top of form
    document.querySelector('.quote-container').scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });

    console.log('Form restarted - ready for new quotation');
  }

  // Multi-step wizard functionality
  const wizard = {
    currentStep: 1,
    totalSteps: 4,
    steps: [
      {
        id: 1,
        title: 'Información General',
        fields: ['empresa', 'nombre', 'correo']
      },
      {
        id: 2,
        title: 'Origen y Destino',
        fields: ['direccion_origen', 'cp_origen', 'direccion_destino', 'cp_destino']
      },
      {
        id: 3,
        title: 'Información de la Carga',
        fields: ['tipo_mercancia', 'numero_un', 'piezas', 'peso', 'dimensiones', 'descripcion_mercancia']
      },
      {
        id: 4,
        title: 'Revisión y Confirmación',
        fields: []
      }
    ]
  };

  // Initialize wizard
  function initializeWizard() {
    createWizardNavigation();
    createProgressIndicator();
    showStep(1);
    updateWizardState();
  }

  // Create wizard navigation
  function createWizardNavigation() {
    const form = document.getElementById('quoteForm');

    // Create navigation container
    const wizardNav = document.createElement('div');
    wizardNav.className = 'wizard-navigation';
    wizardNav.innerHTML = `
      <button type="button" id="prevStep" class="btn-wizard btn-prev" disabled>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="15,18 9,12 15,6"></polyline>
        </svg>
        Anterior
      </button>
      <button type="button" id="nextStep" class="btn-wizard btn-next">
        Siguiente
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="9,18 15,12 9,6"></polyline>
        </svg>
      </button>
    `;

    // Insert navigation at the end of the form
    form.appendChild(wizardNav);

    // Add event listeners
    document.getElementById('prevStep').addEventListener('click', () => previousStep());
    document.getElementById('nextStep').addEventListener('click', () => nextStep());
  }

  // Create progress indicator
  function createProgressIndicator() {
    const form = document.getElementById('quoteForm');

    const progressContainer = document.createElement('div');
    progressContainer.className = 'wizard-progress';
    progressContainer.innerHTML = `
      <div class="progress-steps">
        ${wizard.steps.map((step) => `
          <div class="progress-step clickable-step" data-step="${step.id}" onclick="goToStep(${step.id})">
            <div class="step-number">${step.id}</div>
            <div class="step-title">${step.title}</div>
          </div>
        `).join('')}
      </div>
      <div class="progress-bar">
        <div class="progress-fill"></div>
      </div>
    `;

    // Insert at the beginning of the form
    form.insertBefore(progressContainer, form.firstChild);
  }

  // Show specific step
  function showStep(stepNumber) {
    wizard.currentStep = stepNumber;

    // Hide all form sections with smooth transition
    document.querySelectorAll('.form-section').forEach((section) => {
      section.classList.remove('wizard-visible');
      section.classList.add('wizard-hidden');
    });

    // Show current step section after a brief delay for smooth transition
    setTimeout(() => {
      const sections = document.querySelectorAll('.form-section');
      if (sections[stepNumber - 1]) {
        sections[stepNumber - 1].classList.remove('wizard-hidden');
        sections[stepNumber - 1].classList.add('wizard-visible');
      }
    }, 100);

    // Update progress indicator
    updateProgressIndicator();

    // Update navigation buttons
    updateNavigationButtons();

    // Show/hide submit button and update review data
    const formActions = document.querySelector('.form-actions');

    if (stepNumber === wizard.totalSteps) {
      formActions.style.display = 'block';
      updateReviewData();
    } else {
      formActions.style.display = 'none';
    }

    // Scroll to top of form
    document.querySelector('.quote-container').scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  }

  // Update progress indicator
  function updateProgressIndicator() {
    const progressSteps = document.querySelectorAll('.progress-step');
    const progressFill = document.querySelector('.progress-fill');

    progressSteps.forEach((step, index) => {
      const stepNumber = index + 1;
      step.classList.remove('active', 'completed', 'ready');

      if (stepNumber < wizard.currentStep) {
        step.classList.add('completed');
      } else if (stepNumber === wizard.currentStep) {
        step.classList.add('active');
      }

      // Special case: mark step 4 (Review) as ready when all fields are valid
      if (stepNumber === wizard.totalSteps) {
        const allFieldsValid = checkAllFieldsValid();
        if (allFieldsValid) {
          step.classList.add('ready');
        }
      }
    });

    // Update progress bar
    const progressPercentage = ((wizard.currentStep - 1) / (wizard.totalSteps - 1)) * 100;
    if (progressFill) {
      progressFill.style.width = `${progressPercentage}%`;
    }
  }

  // Update navigation buttons
  function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevStep');
    const nextBtn = document.getElementById('nextStep');

    // Keep Previous button always enabled (except on first step)
    if (prevBtn) {
      prevBtn.disabled = wizard.currentStep === 1;
    }

    if (nextBtn) {
      // Always keep Next button enabled
      nextBtn.disabled = false;
      nextBtn.classList.remove('btn-disabled');

      if (wizard.currentStep === wizard.totalSteps) {
        nextBtn.style.display = 'none';
      } else if (wizard.currentStep === wizard.totalSteps - 1) {
        nextBtn.style.display = 'flex';
        nextBtn.innerHTML = `
          Revisar
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="9,18 15,12 9,6"></polyline>
          </svg>
        `;
      } else {
        nextBtn.style.display = 'flex';
        nextBtn.innerHTML = `
          Siguiente
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="9,18 15,12 9,6"></polyline>
          </svg>
        `;
      }
    }
  }



  // Next step
  function nextStep() {
    // Special case: when going to Review step (step 4), validate all fields
    if (wizard.currentStep === wizard.totalSteps - 1) {
      return goToReviewStep();
    }

    // Allow free navigation for other steps - no validation required
    if (wizard.currentStep < wizard.totalSteps) {
      showStep(wizard.currentStep + 1);
    }
  }

  // Go to review step with validation
  function goToReviewStep() {
    // Validate all fields with force validation
    const isFormValid = validateAllFields(true);

    if (!isFormValid) {
      // Find first invalid field and navigate to its step
      const firstInvalidField = findFirstInvalidField();
      if (firstInvalidField) {
        const stepWithError = findStepForField(firstInvalidField.fieldKey);

        // Show error message
        showValidationMessage(`Por favor, complete todos los campos obligatorios. Revise el campo "${firstInvalidField.label}".`);

        // Navigate to step with error
        showStep(stepWithError);

        // Focus on the field
        setTimeout(() => {
          firstInvalidField.element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          firstInvalidField.element.focus();
        }, 300);

        return;
      }
    }

    // All fields are valid, proceed to review step
    showStep(wizard.totalSteps);
  }

  // Previous step
  function previousStep() {
    if (wizard.currentStep > 1) {
      showStep(wizard.currentStep - 1);
    }
  }

  // Find first invalid field
  function findFirstInvalidField() {
    const fieldLabels = {
      'empresa': 'Empresa',
      'nombre': 'Nombre',
      'correo': 'Correo electrónico',
      'direccion_origen': 'Dirección de origen',
      'cp_origen': 'Código postal de origen',
      'direccion_destino': 'Dirección de destino',
      'cp_destino': 'Código postal de destino',
      'tipo_mercancia': 'Tipo de mercancía',
      'numero_un': 'Número UN',
      'piezas': 'Número de piezas',
      'peso': 'Peso',
      'dimensiones': 'Dimensiones',
      'descripcion_mercancia': 'Descripción de la mercancía'
    };

    for (const [fieldKey, field] of Object.entries(fields)) {
      if (!validationState[fieldKey]) {
        return {
          fieldKey,
          element: field,
          label: fieldLabels[fieldKey] || fieldKey
        };
      }
    }
    return null;
  }

  // Find which step contains a specific field
  function findStepForField(fieldKey) {
    for (let i = 0; i < wizard.steps.length; i++) {
      if (wizard.steps[i].fields.includes(fieldKey)) {
        return i + 1; // Steps are 1-indexed
      }
    }
    return 1; // Default to first step
  }

  // Check if all fields are valid (without forcing validation)
  function checkAllFieldsValid() {
    // Check if all required fields have values and are valid
    const requiredFields = Object.keys(fields);

    for (const fieldKey of requiredFields) {
      const field = fields[fieldKey];
      const value = field.value.trim();

      // Check if field is empty (required fields must have values)
      if (!value) {
        return false;
      }

      // Check if field has been validated and is valid
      if (field.hasBeenInteracted && validationState[fieldKey] === false) {
        return false;
      }
    }

    return true;
  }

  // Show validation message
  function showValidationMessage(message) {
    // Create or update validation message element
    let messageElement = document.getElementById('validation-message');
    if (!messageElement) {
      messageElement = document.createElement('div');
      messageElement.id = 'validation-message';
      messageElement.className = 'validation-message';

      const form = document.getElementById('quoteForm');
      const progressContainer = document.querySelector('.wizard-progress');
      form.insertBefore(messageElement, progressContainer.nextSibling);
    }

    messageElement.textContent = message;
    messageElement.style.display = 'block';

    // Auto-hide after 5 seconds
    setTimeout(() => {
      messageElement.style.display = 'none';
    }, 5000);
  }

  // Update wizard state (without forcing visual validation)
  function updateWizardState() {
    updateNavigationButtons();

    // Keep navigation buttons always enabled for free navigation
    const nextBtn = document.getElementById('nextStep');
    const prevBtn = document.getElementById('prevStep');

    if (nextBtn && wizard.currentStep < wizard.totalSteps) {
      nextBtn.disabled = false;
      nextBtn.classList.remove('btn-disabled');
    }

    if (prevBtn && wizard.currentStep > 1) {
      prevBtn.disabled = false;
      prevBtn.classList.remove('btn-disabled');
    }
  }

  // Update review data
  function updateReviewData() {
    const reviewFields = [
      'empresa', 'nombre', 'correo',
      'direccion_origen', 'cp_origen', 'direccion_destino', 'cp_destino',
      'tipo_mercancia', 'numero_un', 'piezas', 'peso', 'dimensiones', 'descripcion_mercancia'
    ];

    reviewFields.forEach(fieldName => {
      const field = fields[fieldName];
      const reviewElement = document.getElementById(`review-${fieldName}`);

      if (field && reviewElement) {
        let value = field.value.trim();

        // Format specific fields
        if (fieldName === 'tipo_mercancia' && value) {
          const options = {
            'sobredimensionado': 'Sobredimensionado',
            'hazmat': 'Hazmat (Materiales Peligrosos)',
            'perecedero': 'Perecedero',
            'carga_general': 'Carga General'
          };
          value = options[value] || value;
        } else if (fieldName === 'peso' && value) {
          value = `${value} kg`;
        } else if (fieldName === 'piezas' && value) {
          value = `${value} ${parseInt(value) === 1 ? 'pieza' : 'piezas'}`;
        } else if (fieldName === 'dimensiones' && value) {
          // Add unit to dimensions
          const currentUnit = dimensionesUnidad.value;
          const unitConfig = dimensionUnits[currentUnit];
          value = `${value} ${unitConfig.label}`;
        }

        // Special handling for numero_un field
        if (fieldName === 'numero_un') {
          const numeroUnItem = document.getElementById('review-numero_un-item');
          const isHazmatSelected = fields.tipo_mercancia.value === 'hazmat';

          if (isHazmatSelected && value) {
            numeroUnItem.style.display = 'block';
            reviewElement.textContent = value;
            reviewElement.classList.remove('empty');
          } else {
            numeroUnItem.style.display = 'none';
          }
        } else {
          if (value) {
            reviewElement.textContent = value;
            reviewElement.classList.remove('empty');
          } else {
            reviewElement.textContent = 'No especificado';
            reviewElement.classList.add('empty');
          }
        }
      }
    });
  }

  // Navigate to specific step (for edit buttons and clickable steps)
  window.goToStep = function(stepNumber) {
    if (stepNumber >= 1 && stepNumber <= wizard.totalSteps) {
      // Special case: when clicking directly on Review step (step 4), validate all fields
      if (stepNumber === wizard.totalSteps) {
        goToReviewStep();
      } else {
        // Allow free navigation to other steps
        showStep(stepNumber);
      }
    }
  };

  // Override the original updateFormState to work with wizard
  const originalUpdateFormState = updateFormState;
  updateFormState = function() {
    originalUpdateFormState();
    updateWizardState();
    updateProgressIndicator(); // Update progress indicator to reflect field completeness
  };

  // Initialize wizard after DOM is ready
  initializeWizard();

  // Initialize submit button as disabled (don't run validation on load)
  submitBtn.disabled = true;
  submitBtn.classList.add('btn-disabled');

  // Debug: Log wizard initialization
  console.log('Wizard initialized with free navigation enabled');
  console.log('Steps are clickable and navigation is unrestricted');
  console.log('Validation only occurs when going to Review step');

  // Test function for post-submission flow (for development/testing)
  window.testSuccessFlow = function() {
    console.log('Testing success flow...');
    showMessage('Su solicitud de cotización ha sido enviada exitosamente. Nos pondremos en contacto con usted pronto.', 'success');
  };

  // Test function for form restart (for development/testing)
  window.testFormRestart = function() {
    console.log('Testing form restart...');
    performFormRestart();
  };

  // Test function for dimension units (for development/testing)
  window.testDimensionUnits = function() {
    console.log('Testing dimension units...');
    console.log('Current unit:', dimensionesUnidad.value);
    console.log('Unit config:', dimensionUnits[dimensionesUnidad.value]);

    // Test all units
    Object.keys(dimensionUnits).forEach(unit => {
      console.log(`${unit}:`, dimensionUnits[unit]);
    });
  };

  // Test function for UN number field (for development/testing)
  window.testUNField = function() {
    console.log('Testing UN number field...');
    console.log('Current tipo_mercancia:', fields.tipo_mercancia.value);
    console.log('UN field visible:', numeroUnField.style.display !== 'none');
    console.log('UN field required:', fields.numero_un.hasAttribute('required'));
    console.log('UN field value:', fields.numero_un.value);
    console.log('UN validation state:', validationState.numero_un);

    // Test auto-format
    console.log('Testing auto-format:');
    console.log('Input "1203" should become "UN1203"');
    console.log('Input "un1950" should become "UN1950"');
    console.log('Input "UN2794ABC" should become "UN2794"');
  };
});
