<?php
// Configurar manejo de errores para asegurar respuestas JSON
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL);

// Función para enviar respuesta JSON y terminar ejecución
function send_json_response($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// Función para manejar errores fatales
function handle_fatal_error() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log("Fatal error in form processing: " . $error['message'] . " in " . $error['file'] . " on line " . $error['line']);
        send_json_response([
            'success' => false,
            'message' => 'Hubo un problema interno al procesar tu solicitud. Por favor, intenta de nuevo más tarde.'
        ], 500);
    }
}

// Registrar el manejador de errores fatales
register_shutdown_function('handle_fatal_error');

// Configurar cabeceras para evitar caché y establecer el contenido como JSON
header('Content-Type: application/json');

// Incluir la configuración con manejo de errores
try {
    require 'config.php';
} catch (Exception $e) {
    error_log("Error loading config: " . $e->getMessage());
    send_json_response([
        'success' => false,
        'message' => 'Error de configuración del servidor.'
    ], 500);
}

// Función para limpiar y validar los datos de entrada
function sanitize_input($data) {
    return htmlspecialchars(stripslashes(trim($data)));
}

// Función para validar datos con reglas específicas
function validate_input($data, $type) {
    $data = sanitize_input($data);
    $error = null;

    switch ($type) {
        case 'nombre':
        case 'apellido':
            // Solo letras, espacios y acentos/ñ. Mínimo 2 caracteres.
            if (empty($data)) {
                $error = 'Este campo es obligatorio.';
            } elseif (!preg_match('/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]{2,}$/', $data)) {
                $error = 'Solo se permiten letras y espacios (mínimo 2 caracteres).';
            }
            break;

        case 'email':
            if (empty($data)) {
                $error = 'El correo electrónico es obligatorio.';
            } elseif (!filter_var($data, FILTER_VALIDATE_EMAIL)) {
                $error = 'Formato de correo electrónico inválido.';
            }
            break;

        case 'telefono':
            if (empty($data)) {
                $error = 'El teléfono es obligatorio.';
            } elseif (!preg_match('/^[\d\s\-\+\(\)]{7,}$/', $data)) {
                $error = 'Formato de teléfono inválido.';
            }
            break;

        case 'empresa':
            if (empty($data)) {
                $error = 'El nombre de la empresa es obligatorio.';
            } elseif (!preg_match('/^[\wÁÉÍÓÚáéíóúñÑ\s\-\.\&,]{2,}$/u', $data)) {
                $error = 'Formato de nombre de empresa inválido.';
            }
            break;

        case 'cargo':
            if (empty($data)) {
                $error = 'Selecciona un cargo.';
            }
            break;

        case 'mensaje':
            if (empty($data)) {
                $error = 'El mensaje es obligatorio.';
            } elseif (strlen($data) < 10) {
                $error = 'El mensaje debe tener al menos 10 caracteres.';
            }
            break;
    }

    return ['value' => $data, 'error' => $error];
}

// Verificar que la solicitud sea POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Inicializar array de errores
    $errors = [];

    // Validar cada campo
    $nombre_result = validate_input(isset($_POST['nombre']) ? $_POST['nombre'] : '', 'nombre');
    $nombre = $nombre_result['value'];
    if ($nombre_result['error']) $errors['nombre'] = $nombre_result['error'];

    $apellido_result = validate_input(isset($_POST['apellido']) ? $_POST['apellido'] : '', 'apellido');
    $apellido = $apellido_result['value'];
    if ($apellido_result['error']) $errors['apellido'] = $apellido_result['error'];

    $correo_result = validate_input(isset($_POST['correo']) ? $_POST['correo'] : '', 'email');
    $correo = $correo_result['value'];
    if ($correo_result['error']) $errors['correo'] = $correo_result['error'];

    $telefono_result = validate_input(isset($_POST['telefono']) ? $_POST['telefono'] : '', 'telefono');
    $telefono = $telefono_result['value'];
    if ($telefono_result['error']) $errors['telefono'] = $telefono_result['error'];

    $empresa_result = validate_input(isset($_POST['empresa']) ? $_POST['empresa'] : '', 'empresa');
    $empresa = $empresa_result['value'];
    if ($empresa_result['error']) $errors['empresa'] = $empresa_result['error'];

    $cargo_result = validate_input(isset($_POST['cargo']) ? $_POST['cargo'] : '', 'cargo');
    $cargo = $cargo_result['value'];
    if ($cargo_result['error']) $errors['cargo'] = $cargo_result['error'];

    $mensaje_result = validate_input(isset($_POST['mensaje']) ? $_POST['mensaje'] : '', 'mensaje');
    $mensaje = $mensaje_result['value'];
    if ($mensaje_result['error']) $errors['mensaje'] = $mensaje_result['error'];

    $recaptcha_response = isset($_POST['g-recaptcha-response']) ? $_POST['g-recaptcha-response'] : '';

    // Si hay errores, devolver respuesta con errores
    if (!empty($errors)) {
        send_json_response([
            'success' => false,
            'message' => 'Hay errores en el formulario.',
            'errors' => $errors
        ], 400);
    }

    // Verificar reCAPTCHA
    try {
        $recaptcha_secret = RECAPTCHA_SECRET; // Tu secret key en config.php

        if (empty($recaptcha_secret)) {
            error_log("reCAPTCHA secret key not configured");
            send_json_response([
                'success' => false,
                'message' => 'Error de configuración del servidor.'
            ], 500);
        }

        $recaptcha_url = 'https://www.google.com/recaptcha/api/siteverify';

        // Usar cURL en lugar de file_get_contents para mejor manejo de errores
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $recaptcha_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
            'secret' => $recaptcha_secret,
            'response' => $recaptcha_response
        ]));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $response = curl_exec($ch);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error) {
            error_log("cURL error in reCAPTCHA verification: " . $curl_error);
            send_json_response([
                'success' => false,
                'message' => 'Error al verificar reCAPTCHA. Por favor, intenta de nuevo.'
            ], 500);
        }

        $responseKeys = json_decode($response, true);

        if (!$responseKeys || !isset($responseKeys["success"])) {
            error_log("Invalid reCAPTCHA response: " . $response);
            send_json_response([
                'success' => false,
                'message' => 'Error al verificar reCAPTCHA. Por favor, intenta de nuevo.'
            ], 500);
        }

        if (!$responseKeys["success"]) {
            send_json_response([
                'success' => false,
                'message' => 'Error en la verificación de reCAPTCHA. Por favor, completa el reCAPTCHA e intenta de nuevo.'
            ], 400);
        }
    } catch (Exception $e) {
        error_log("Exception in reCAPTCHA verification: " . $e->getMessage());
        send_json_response([
            'success' => false,
            'message' => 'Error al verificar reCAPTCHA. Por favor, intenta de nuevo.'
        ], 500);
    }

    // Conectar a la base de datos
    try {
        // Verificar que las constantes de DB estén definidas
        if (!defined('DB_HOST') || !defined('DB_USER') || !defined('DB_PASSWORD') || !defined('DB_DATABASE')) {
            error_log("Database configuration constants not defined");
            send_json_response([
                'success' => false,
                'message' => 'Error de configuración del servidor.'
            ], 500);
        }

        if (empty(DB_HOST) || empty(DB_USER) || empty(DB_DATABASE)) {
            error_log("Database configuration values are empty");
            send_json_response([
                'success' => false,
                'message' => 'Error de configuración del servidor.'
            ], 500);
        }

        $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_DATABASE);

        // Verificar la conexión
        if ($mysqli->connect_errno) {
            // Registrar el error en un archivo de log
            error_log("Error de conexión a la base de datos: " . $mysqli->connect_error);

            // Devolver un mensaje genérico al usuario (sin exponer detalles sensibles)
            send_json_response([
                'success' => false,
                'message' => 'Hubo un problema al procesar tu solicitud. Por favor, intenta de nuevo más tarde.'
            ], 500);
        }

        // Establecer el conjunto de caracteres
        if (!$mysqli->set_charset("utf8mb4")) {
            error_log("Error setting charset: " . $mysqli->error);
            send_json_response([
                'success' => false,
                'message' => 'Error de configuración del servidor.'
            ], 500);
        }
    } catch (Exception $e) {
        // Registrar el error en un archivo de log
        error_log("Excepción al conectar a la base de datos: " . $e->getMessage());

        // Devolver un mensaje genérico al usuario
        send_json_response([
            'success' => false,
            'message' => 'Hubo un problema al procesar tu solicitud. Por favor, intenta de nuevo más tarde.'
        ], 500);
    }

    // Preparar la sentencia SQL para prevenir inyecciones SQL
    $stmt = $mysqli->prepare("
        INSERT INTO contactos (nombre, apellido, correo, telefono, empresa, cargo, mensaje)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    if (!$stmt) {
        error_log("Error preparing SQL statement: " . $mysqli->error);
        send_json_response([
            'success' => false,
            'message' => 'Error al preparar la consulta.'
        ], 500);
    }

    // Vincular los parámetros y ejecutar
    if (!$stmt->bind_param("sssssss", $nombre, $apellido, $correo, $telefono, $empresa, $cargo, $mensaje)) {
        error_log("Error binding parameters: " . $stmt->error);
        send_json_response([
            'success' => false,
            'message' => 'Error al procesar los datos.'
        ], 500);
    }

    if ($stmt->execute()) {
        // ==============================
        // 1. Armamos el cuerpo del correo
        // ==============================
        try {
            $destinatario = EMAIL_TO; // Configurado en config.php

            if (empty($destinatario)) {
                error_log("EMAIL_TO not configured");
                // Continuar sin enviar email pero guardar en BD
                send_json_response([
                    'success' => true,
                    'message' => 'Formulario enviado exitosamente. (Nota: configuración de email pendiente)'
                ], 200);
            }

            $asunto = 'Nuevo Contacto desde el Formulario';

            // Mensaje en texto plano (puedes agregar HTML si gustas)
            $cuerpoMensaje =
                "Has recibido un nuevo mensaje:\n\n" .
                "Nombre:   $nombre $apellido\n" .
                "Correo:   $correo\n" .
                "Teléfono: $telefono\n" .
                "Empresa:  $empresa\n" .
                "Cargo:    $cargo\n\n" .
                "Mensaje:\n$mensaje\n";

            // 2. Cabeceras (para 'From' y 'Reply-To')
            // IMPORTANTE: Para evitar filtrado de spam, a veces conviene usar
            // 'From: <EMAIL>' en lugar del correo del usuario.
            // Y 'Reply-To' para que puedas responderle directamente.
            $headers  = "From: " . EMAIL_FROM . "\r\n";
            $headers .= "Reply-To: " . $correo . "\r\n";
            $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

            // 3. Enviamos el correo con la función nativa mail()
            // Ten en cuenta que la entrega puede depender de la configuración
            // del servidor (sendmail, postfix, etc.).
            if (mail($destinatario, $asunto, $cuerpoMensaje, $headers)) {
                send_json_response([
                    'success' => true,
                    'message' => 'Formulario enviado exitosamente.'
                ], 200);
            } else {
                error_log("Failed to send email to: " . $destinatario);
                // Aún así consideramos exitoso porque se guardó en BD
                send_json_response([
                    'success' => true,
                    'message' => 'Formulario enviado exitosamente. (El correo de notificación puede tardar en llegar)'
                ], 200);
            }
        } catch (Exception $e) {
            error_log("Exception sending email: " . $e->getMessage());
            // Aún así consideramos exitoso porque se guardó en BD
            send_json_response([
                'success' => true,
                'message' => 'Formulario enviado exitosamente. (El correo de notificación puede tardar en llegar)'
            ], 200);
        }

    } else {
        error_log("Error executing SQL statement: " . $stmt->error);
        send_json_response([
            'success' => false,
            'message' => 'Error al guardar los datos en la base de datos.'
        ], 500);
    }

    // Cerrar conexiones
    $stmt->close();
    $mysqli->close();

} else {
    // Si no es una solicitud POST, rechazar
    send_json_response([
        'success' => false,
        'message' => 'Método de solicitud no permitido.'
    ], 405);
}
?>
