<?php
// Cargar variables de entorno desde .env si existe
if (file_exists(__DIR__ . '/.env')) {
    $env_lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($env_lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value);

            // Eliminar comillas si existen
            if (preg_match('/^([\'"])(.*)\1$/', $value, $matches)) {
                $value = $matches[2];
            }

            $_ENV[$key] = $value;
            putenv("$key=$value");
        }
    }
}

// Función para obtener variables de entorno con valor por defecto
function env($key, $default = null) {
    $value = getenv($key);
    if ($value === false) {
        return $default;
    }
    return $value;
}

// Configuración de la Base de Datos
define('DB_HOST', env('DB_HOST'));
define('DB_USER', env('DB_USER'));
define('DB_PASSWORD', env('DB_PASSWORD'));
define('DB_DATABASE', env('DB_DATABASE'));

// Configuración del Correo Electrónico
define('EMAIL_HOST', env('EMAIL_HOST'));
define('EMAIL_USERNAME', env('EMAIL_USERNAME'));
define('EMAIL_PASSWORD', env('EMAIL_PASSWORD'));
define('EMAIL_PORT', env('EMAIL_PORT'));
define('EMAIL_FROM', env('EMAIL_FROM'));
define('EMAIL_FROM_NAME', env('EMAIL_FROM_NAME', 'STI Logística'));
define('EMAIL_TO', env('EMAIL_TO'));

// Configuración de Operadores
define('CORREO_OPERADOR_1', env('CORREO_OPERADOR_1'));
define('CORREO_OPERADOR_2', env('CORREO_OPERADOR_2'));
define('CORREO_OPERADOR_3', env('CORREO_OPERADOR_3'));
define('CORREO_OPERADOR_4', env('CORREO_OPERADOR_4'));

// Configuración de reCAPTCHA
define('RECAPTCHA_SECRET', env('RECAPTCHA_SECRET'));
?>
