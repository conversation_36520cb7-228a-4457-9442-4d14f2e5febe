*,
*::after,
*::before {
    box-sizing: border-box;
}

:root {
    --primary-blue: #0058A8;
    --primary-green: #009D5A;
    --primary-white: #FFFFFF;
    --primary-black: #000319;

    /* Font sizes for normalization */
    --font-size-xs: 0.75rem;   /* 12px */
    --font-size-s: 0.875rem;   /* 14px */
    --font-size-m: 1rem;       /* 16px */
    --font-size-l: 1.25rem;    /* 20px */
    --font-size-xl: 1.5rem;    /* 24px */

    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

}


html {
    scroll-behavior: smooth;
}